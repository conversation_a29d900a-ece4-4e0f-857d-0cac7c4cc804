import 'dart:io';
import 'package:flutter/material.dart';

import '../core/app_state.dart';
import '../core/dependency_injection.dart';
import '../services/auth_service.dart';
import '../services/backend_service.dart';
import '../services/api_service.dart';
import '../services/websocket_service.dart';
import '../services/data_manager.dart';
import '../services/log_service.dart';
import '../services/auto_start_service.dart';
import '../services/routing_settings_service.dart';
import '../widgets/svg_asset_widget.dart';
import '../widgets/custom_window_frame.dart';
import '../widgets/design_system_sidebar.dart';
import '../utils/design_system.dart';
import '../utils/api_exception.dart';
import '../utils/constants.dart';
import '../utils/simple_encryptor.dart';
import '../models/user_info.dart';
import '../generated/l10n/app_localizations.dart';

import '../services/platform/cross_platform_storage_service.dart';
import '../services/platform/platform_service_factory.dart';
import '../services/network_permission_service.dart';
import '../services/lookup_service.dart';

/// 登录屏幕 - 基于新设计系统
class LoginScreen extends StatefulWidget {
  final bool clearPasswordOnInit;

  const LoginScreen({Key? key, this.clearPasswordOnInit = false}) : super(key: key);

  /// 静态方法：创建一个确保密码已清除的登录屏幕
  /// 用于从注销流程导航到登录屏幕时调用
  static Widget createAfterLogout() {
    return const LoginScreen(clearPasswordOnInit: true);
  }

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen>
    with TickerProviderStateMixin {
  late final AuthService _authService;
  late final LogService _logService;
  late final CrossPlatformStorageService _storageService;

  // 表单控制器
  final _domainController = TextEditingController();
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();

  // 表单和焦点管理
  final _formKey = GlobalKey<FormState>();
  final _usernameFocusNode = FocusNode();
  final _passwordFocusNode = FocusNode();

  // 表单状态
  bool _isLoading = false;
  bool _rememberCredentials = false;
  bool _showPassword = false;
  String _errorMessage = '';

  // 登录步骤：0=域名输入，1=用户名密码输入
  int _loginStep = 0;

  // 暂存从lookup查询到的服务器列表URL
  String _serverListUrl = '';

  // 动画控制器
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeServices();
    _initializeAnimations();
    _loadSavedCredentials();
  }

  void _initializeServices() {
    _authService = serviceLocator<AuthService>();
    _logService = serviceLocator<LogService>();
    // 使用已经初始化的跨平台存储服务实例
    _storageService = serviceLocator<CrossPlatformStorageService>();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: DesignSystem.animationMedium,
      vsync: this,
    );
    _slideController = AnimationController(
      duration: DesignSystem.animationMedium,
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: DesignSystem.curveStandard,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: DesignSystem.curveEmphasized,
    ));

    _fadeController.forward();
    _slideController.forward();
  }

  /// 加载保存的凭据
  Future<void> _loadSavedCredentials() async {
    try {
      final encryptor = SimpleEncryptor();

      // 如果是从注销流程创建的登录屏幕，强制清除密码数据
      if (widget.clearPasswordOnInit) {
        _logService.info('LoginScreen', 'Detected creation from logout flow, force clearing password data');
        await _forceCleanPasswordData();
        // 继续加载其他非敏感数据（域名、用户名）
      }

      // 注释掉AuthService认证状态检查，允许在应用重启后加载保存的凭据
      // 只有在明确注销时才清除密码数据，而不是在每次应用启动时检查
      // if (!_authService.isAuthenticated) {
      //   _logService.info('LoginScreen', 'Detected user logout, force clearing password data');
      //   await _forceCleanPasswordData();
      //   // 继续加载其他非敏感数据（域名、用户名）
      // }

      // 加载保存的客户域
      final savedDomain = await _storageService.getString(StorageKeys.domain);
      if (savedDomain != null && savedDomain.isNotEmpty) {
        setState(() {
          _domainController.text = savedDomain;
          // 如果已经有保存的域，直接进入凭据输入步骤
          _loginStep = 1;
        });
        _logService.info('LoginScreen', 'Loaded saved client domain');

        // 预先查询服务器列表URL
        try {
          final lookupService = LookupService();
          final lookupResult = await lookupService.getServerListUrl(savedDomain);
          _serverListUrl = lookupResult.serverListUrl;
          _logService.info('LoginScreen', 'Pre-cached server URL for saved domain: $_serverListUrl');
        } catch (e) {
          _logService.warning('LoginScreen', 'Failed to pre-cache server URL for saved domain: $e');
          // 即使域名查找失败，也要继续自动登录检查
        }
      } else if (_authService.domain.isNotEmpty) {
        // 如果AuthService中有域信息，也使用它
        setState(() {
          _domainController.text = _authService.domain;
          _loginStep = 1;
        });
        _logService.info('LoginScreen', 'Using domain information from AuthService');

        // 预先查询服务器列表URL
        try {
          final lookupService = LookupService();
          final lookupResult = await lookupService.getServerListUrl(_authService.domain);
          _serverListUrl = lookupResult.serverListUrl;
          _logService.info('LoginScreen', 'Pre-cached server URL for AuthService domain: $_serverListUrl');
        } catch (e) {
          _logService.warning('LoginScreen', 'Failed to pre-cache server URL for AuthService domain: $e');
          // 即使域名查找失败，也要继续自动登录检查
        }
      }

      // 加载保存的用户名
      final savedUsername = await _storageService.getString(StorageKeys.username);
      if (savedUsername != null && savedUsername.isNotEmpty) {
        setState(() {
          _usernameController.text = savedUsername;
        });
        _logService.info('LoginScreen', 'Loaded saved username');
      }

      // 检查是否记住凭据
      final rememberCredentials = await _storageService.getBool(StorageKeys.rememberCredentials) ?? false;

      // 只有在用户选择记住凭据且不是从注销流程创建时才加载密码
      if (rememberCredentials && !widget.clearPasswordOnInit) {
        final savedPassword = await _storageService.getSecureString(StorageKeys.password);
        if (savedPassword != null && savedPassword.isNotEmpty) {
          try {
            final decryptedPassword = await encryptor.decrypt(savedPassword);
            if (decryptedPassword.isNotEmpty) {
              setState(() {
                _passwordController.text = decryptedPassword;
                _rememberCredentials = true;
              });
              _logService.info('LoginScreen', 'Loaded saved password');
            } else {
              // 解密后密码为空，清除相关数据
              await _clearPasswordData();
            }
          } catch (e) {
            _logService.warning('LoginScreen', 'Failed to decrypt saved password: $e');
            // 清除无效的密码数据
            await _clearPasswordData();
          }
        } else {
          // 没有保存的密码，确保密码字段为空
          _ensurePasswordFieldEmpty();
        }
      } else {
        // 用户没有选择记住凭据或从注销流程创建，确保密码字段为空
        _ensurePasswordFieldEmpty();
        if (widget.clearPasswordOnInit) {
          _logService.info('LoginScreen', 'Password field cleared due to logout flow');
        } else {
          _logService.info('LoginScreen', 'User did not choose to remember credentials, password field cleared');
        }
      }

      // 检查是否应该自动登录（在所有数据加载完成后）
      await _checkAutoLogin();
    } catch (e) {
      _logService.error('LoginScreen', 'Failed to load saved credentials', e);
      // 出错时确保密码字段为空
      _ensurePasswordFieldEmpty();
    }
  }

  /// 检查是否应该自动登录
  Future<void> _checkAutoLogin() async {
    try {
      // 检查应用状态，避免在后台状态下触发自动登录
      final appState = WidgetsBinding.instance.lifecycleState;
      _logService.info('LoginScreen', 'Current app lifecycle state: $appState');

      // 在应用启动时，lifecycleState可能为null或其他状态，我们需要更宽松的检查
      // 只有在明确处于paused状态时才跳过自动登录，null状态允许继续
      if (appState == AppLifecycleState.paused) {
        _logService.info('LoginScreen', 'App is paused, skipping auto-login to avoid background service start issues');
        return;
      }

      final autoStartService = serviceLocator<AutoStartService>();
      final shouldAutoLogin = await autoStartService.shouldAutoLogin();

      // 详细记录自动登录条件检查
      _logService.info('LoginScreen', 'Auto-login condition check:');
      _logService.info('LoginScreen', '  shouldAutoLogin: $shouldAutoLogin');
      _logService.info('LoginScreen', '  domain not empty: ${_domainController.text.isNotEmpty}');
      _logService.info('LoginScreen', '  domain value: "${_domainController.text}"');
      _logService.info('LoginScreen', '  username not empty: ${_usernameController.text.isNotEmpty}');
      _logService.info('LoginScreen', '  username value: "${_usernameController.text}"');
      _logService.info('LoginScreen', '  password not empty: ${_passwordController.text.isNotEmpty}');
      _logService.info('LoginScreen', '  remember credentials: $_rememberCredentials');

      if (shouldAutoLogin &&
          _domainController.text.isNotEmpty &&
          _domainController.text != 'https://www.unisase.cn/files/mobileserver.json' &&
          _usernameController.text.isNotEmpty &&
          _passwordController.text.isNotEmpty &&
          _rememberCredentials) {

        _logService.info('LoginScreen', 'Auto-login conditions met, starting auto-login');

        // 延迟一小段时间让UI完全加载
        await Future.delayed(const Duration(milliseconds: 500));

        // 再次检查应用状态和组件状态
        final currentAppState = WidgetsBinding.instance.lifecycleState;
        // 只有在明确处于paused状态时才取消自动登录，null和resumed状态都允许继续
        if (currentAppState == AppLifecycleState.paused) {
          _logService.info('LoginScreen', 'App state changed to $currentAppState during delay, cancelling auto-login');
          return;
        }

        // 如果组件仍然挂载且应用在前台，执行自动登录
        if (mounted && !_isLoading) {
          await _handleLogin();
        }
      } else {
        _logService.info('LoginScreen', 'Auto-login conditions not met - shouldAutoLogin: $shouldAutoLogin, hasCredentials: ${_domainController.text.isNotEmpty && _usernameController.text.isNotEmpty && _passwordController.text.isNotEmpty && _rememberCredentials}');
      }
    } catch (e) {
      _logService.error('LoginScreen', 'Failed to check auto-login', e);
    }
  }

  /// 清除密码相关数据
  Future<void> _clearPasswordData() async {
    await _storageService.removeSecureString(StorageKeys.password);
    await _storageService.remove(StorageKeys.rememberCredentials);
    _ensurePasswordFieldEmpty();
    _logService.info('LoginScreen', 'Cleared invalid password data');
  }

  /// 确保密码字段为空
  void _ensurePasswordFieldEmpty() {
    setState(() {
      _passwordController.text = '';
      _rememberCredentials = false;
    });
  }

  /// 强制清除所有密码相关数据
  /// 在从注销返回时调用，确保完全清除敏感信息
  Future<void> _forceCleanPasswordData() async {
    try {
      // 清除所有密码相关的持久化数据
      await _storageService.removeSecureString(StorageKeys.password);
      await _storageService.remove(StorageKeys.rememberCredentials);

      // 清除UI中的密码字段
      _ensurePasswordFieldEmpty();

      _logService.info('LoginScreen', 'Force clear password data completed');
    } catch (e) {
      _logService.error('LoginScreen', 'Failed to force clear password data', e);
      // 即使出错也要确保UI字段为空
      _ensurePasswordFieldEmpty();
    }
  }

  @override
  void dispose() {
    _domainController.dispose();
    _usernameController.dispose();
    _passwordController.dispose();
    _usernameFocusNode.dispose();
    _passwordFocusNode.dispose();
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // 检测是否为移动端
    final isMobile = Platform.isIOS || Platform.isAndroid;

    if (isMobile) {
      // 移动端布局
      return Scaffold(
        body: Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
              colors: AppColors.backgroundGradient,
            ),
          ),
          child: Platform.isIOS
              ? SafeArea(child: _buildMainContent()) // iOS需要SafeArea
              : _buildMainContent(), // Android不需要SafeArea
        ),
      );
    } else {
      // 桌面端布局
      final desktopContent = Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
            colors: AppColors.backgroundGradient,
          ),
        ),
        child: Row(
          children: [
            // 侧边栏
            _buildSidebar(),

            // 主要内容区域
            Expanded(
              child: Stack(
                children: [
                  // 主要内容
                  _buildMainContent(),

                  // 底部遮罩
                  Positioned(
                    bottom: 0,
                    left: 0,
                    right: 0,
                    child: DesignSystemSvgs.maskOverlay(
                      width: double.infinity,
                      height: 100,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );

      return AppWindowWrapper(
        child: (Platform.isWindows || Platform.isLinux)
          ? CustomWindowFrame(child: desktopContent) // Windows/Linux - use custom window frame
          : Platform.isMacOS
            ? FutureBuilder<bool>(
                future: PlatformServiceFactory.isIOSAppOnMacOS(),
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return desktopContent; // While checking, don't use CustomWindowFrame
                  }

                  final isIOSAppOnMac = snapshot.data ?? false;
                  if (isIOSAppOnMac) {
                    return desktopContent; // iOS App on macOS - no custom window frame
                  } else {
                    return CustomWindowFrame(child: desktopContent); // Native macOS - use custom window frame
                  }
                },
              )
            : desktopContent, // Other platforms - no custom window frame
      );
    }
  }

  /// 构建侧边栏
  Widget _buildSidebar() {
    // 创建一个空的用户信息用于侧边栏显示
    final emptyUserInfo = UserInfo();

    return DesignSystemSidebar(
      selectedIndex: -1, // 登录界面不选中任何项
      onItemSelected: (index) {
        // 登录界面不响应侧边栏点击
      },
      userInfo: emptyUserInfo,
      onLogout: () {
        // 登录界面不需要退出功能
      },
      enableTabNavigation: false, // 禁用侧边栏的Tab导航，避免干扰登录表单
    );
  }

  /// 构建主要内容
  Widget _buildMainContent() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: Center(
          child: SingleChildScrollView(
            child: Container(
              width: _getContentWidth(context),
              padding: EdgeInsets.all(_getContentPadding(context)),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Logo区域
                  _buildLogoArea(),

                  const SizedBox(height: DesignSystem.spacing46),

                  // 登录表单
                  _buildLoginForm(),

                  const SizedBox(height: DesignSystem.spacing24),

                  // 错误信息
                  if (_errorMessage.isNotEmpty) _buildErrorMessage(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// 获取内容区域宽度
  double _getContentWidth(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = MediaQuery.of(context).size.shortestSide >= 768;

    if (isTablet) {
      // iPad等平板设备：使用屏幕宽度的60%，但不超过500px，不少于400px
      return (screenWidth * 0.6).clamp(400.0, 500.0);
    } else {
      // iPhone等手机设备：使用屏幕宽度的90%，但不超过400px
      return (screenWidth * 0.9).clamp(280.0, 400.0);
    }
  }

  /// 获取内容区域内边距
  double _getContentPadding(BuildContext context) {
    final isTablet = MediaQuery.of(context).size.shortestSide >= 768;
    return isTablet ? DesignSystem.spacing32 : DesignSystem.spacing24;
  }

  /// 构建Logo区域
  Widget _buildLogoArea() {
    return Column(
      children: [
        // 只显示盾牌Logo
        DesignSystemSvgs.shieldLogo(
          width: 120,
          height: 135,
        ),
      ],
    );
  }

  /// 构建登录表单
  Widget _buildLoginForm() {
    return Form(
      key: _formKey,
      child: Container(
        padding: const EdgeInsets.all(DesignSystem.spacing24),
        decoration: BoxDecoration(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(DesignSystem.radiusLarge),
        ),
        child: Column(
          children: [
            if (_loginStep == 0) ..._buildDomainStep(),
            if (_loginStep == 1) ..._buildCredentialsStep(),
          ],
        ),
      ),
    );
  }

  /// 构建域名输入步骤
  List<Widget> _buildDomainStep() {
    final l10n = AppLocalizations.of(context)!;

    return [
      // 域名输入框上方提示文本
      Align(
        alignment: Alignment.centerLeft,
        child: Padding(
          padding: const EdgeInsets.only(bottom: DesignSystem.spacing8),
          child: Text(
            l10n.clientDomain,
            style: DesignSystem.bodySmall.copyWith(
              color: AppColors.textDarkGray,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),

      _buildTextField(
        controller: _domainController,
        label: l10n.clientDomain,
        hint: "research.staff.panabit",
        prefixIcon: Icons.language,
        textInputAction: TextInputAction.next,
        onSubmitted: (_) => _handleDomainNext(),
      ),

      const SizedBox(height: DesignSystem.spacing24),

      _buildActionButton(
        text: l10n.nextStep,
        onPressed: _handleDomainNext,
        isLoading: _isLoading,
      ),
    ];
  }

  /// 构建凭据输入步骤
  List<Widget> _buildCredentialsStep() {
    final l10n = AppLocalizations.of(context)!;

    return [
      // 域名显示和更改选项
      Container(
        padding: const EdgeInsets.all(DesignSystem.spacing12),
        decoration: BoxDecoration(
          color: Colors.transparent, // 客户域已填充时使用透明背景
          borderRadius: BorderRadius.circular(DesignSystem.radiusMedium),
          border: Border.all(
            color: AppColors.inputBorder,
            width: 1,
          ),
        ),
        child: Row(
          children: [
            const Icon(Icons.language, color: AppColors.iconSecondary, size: 20),
            const SizedBox(width: DesignSystem.spacing8),
            Expanded(
              child: Text(
                '${l10n.clientDomain}: ${_domainController.text}',
                style: DesignSystem.bodySmall.copyWith(
                  color: AppColors.textDarkGray, // 使用深灰色文字适配白色背景
                ),
              ),
            ),
            InkWell(
              onTap: () {
                setState(() {
                  _loginStep = 0;
                  _errorMessage = '';
                });
              },
              child: Padding(
                padding: const EdgeInsets.all(DesignSystem.spacing4),
                child: Text(
                  l10n.change,
                  style: DesignSystem.bodySmall.copyWith(
                    color: AppColors.primary, // 使用主色调突出显示更改按钮
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),

      const SizedBox(height: DesignSystem.spacing16),

      // 用户名输入框上方提示文本
      Align(
        alignment: Alignment.centerLeft,
        child: Padding(
          padding: const EdgeInsets.only(bottom: DesignSystem.spacing8),
          child: Text(
            l10n.username,
            style: DesignSystem.bodySmall.copyWith(
              color: AppColors.textDarkGray,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),

      _buildTextField(
        controller: _usernameController,
        label: l10n.username,
        hint: l10n.pleaseEnterUsername,
        prefixIcon: Icons.person,
        focusNode: _usernameFocusNode,
        textInputAction: TextInputAction.next,
        onSubmitted: (_) => _passwordFocusNode.requestFocus(),
      ),

      const SizedBox(height: DesignSystem.spacing16),

      // 密码输入框上方提示文本
      Align(
        alignment: Alignment.centerLeft,
        child: Padding(
          padding: const EdgeInsets.only(bottom: DesignSystem.spacing8),
          child: Text(
            l10n.password,
            style: DesignSystem.bodySmall.copyWith(
              color: AppColors.textDarkGray,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),

      _buildTextField(
        controller: _passwordController,
        label: l10n.password,
        hint: l10n.pleaseEnterPassword,
        prefixIcon: Icons.lock,
        isPassword: true,
        showPassword: _showPassword,
        onTogglePassword: () => setState(() => _showPassword = !_showPassword),
        focusNode: _passwordFocusNode,
        textInputAction: TextInputAction.done,
        onSubmitted: (_) => _handleLogin(),
      ),

      const SizedBox(height: DesignSystem.spacing16),

      // 记住凭据选项
      Row(
        children: [
          Checkbox(
            value: _rememberCredentials,
            onChanged: (value) => setState(() => _rememberCredentials = value ?? false),
            activeColor: AppColors.primary,
          ),
          Expanded(
            child: Text(
              l10n.rememberUsernamePassword,
              style: DesignSystem.bodySmall.copyWith(
                color: AppColors.textDarkGray,
              ),
            ),
          ),
        ],
      ),

      const SizedBox(height: DesignSystem.spacing24),

      _buildActionButton(
        text: l10n.loginButton,
        onPressed: _handleLogin,
        isLoading: _isLoading,
      ),
    ];
  }

  /// 构建文本输入框
  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData prefixIcon,
    bool isPassword = false,
    bool showPassword = false,
    VoidCallback? onTogglePassword,
    FocusNode? focusNode,
    TextInputAction? textInputAction,
    Function(String)? onSubmitted,
  }) {
    return TextField(
      controller: controller,
      focusNode: focusNode,
      obscureText: isPassword && !showPassword,
      textInputAction: textInputAction,
      onSubmitted: onSubmitted,
      style: DesignSystem.bodyMedium.copyWith(
        color: AppColors.textPrimary,
      ),
      decoration: InputDecoration(
        hintText: hint,
        hintStyle: DesignSystem.bodyMedium.copyWith(
          color: AppColors.textPlaceholder,
        ),
        prefixIcon: Icon(
          prefixIcon,
          color: AppColors.iconSecondary,
        ),
        suffixIcon: isPassword
            ? IconButton(
                onPressed: onTogglePassword,
                icon: Icon(
                  showPassword ? Icons.visibility_off : Icons.visibility,
                  color: AppColors.iconSecondary,
                ),
              )
            : null,
        filled: true,
        fillColor: AppColors.inputBackground,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(DesignSystem.radiusMedium),
          borderSide: const BorderSide(color: AppColors.inputBorder),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(DesignSystem.radiusMedium),
          borderSide: const BorderSide(color: AppColors.inputBorder),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(DesignSystem.radiusMedium),
          borderSide: const BorderSide(color: AppColors.inputFocusBorder, width: 2),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: DesignSystem.spacing16,
          vertical: DesignSystem.spacing12,
        ),
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButton({
    required String text,
    required VoidCallback? onPressed,
    required bool isLoading,
  }) {
    return SizedBox(
      width: double.infinity,
      height: 48,
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(DesignSystem.radiusMedium),
          ),
          elevation: 0,
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 24), // 增加垂直内边距
        ),
        child: isLoading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : Text(
                text,
                style: DesignSystem.bodyMedium.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                  leadingDistribution: TextLeadingDistribution.even, // 确保文字居中
                ),
              ),
      ),
    );
  }

  /// 构建错误信息
  Widget _buildErrorMessage() {
    return Container(
      padding: const EdgeInsets.all(DesignSystem.spacing12),
      decoration: BoxDecoration(
        color: AppColors.error.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(DesignSystem.radiusMedium),
        border: Border.all(color: AppColors.error.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.error_outline,
            color: AppColors.error,
            size: 20,
          ),
          const SizedBox(width: DesignSystem.spacing8),
          Expanded(
            child: Text(
              _errorMessage,
              style: DesignSystem.bodySmall.copyWith(
                color: AppColors.error,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 确保server list URL可用
  ///
  /// DESCRIPTION:
  ///     在登录前检查server list URL状态，如果为空则重新获取
  ///     这解决了网络恢复后不重新获取server list的问题
  ///
  /// RETURNS:
  ///     Future<void> - 异步操作完成标识
  ///
  /// THROWS:
  ///     Exception - 如果无法获取server list URL
  Future<void> _ensureServerListUrl() async {
    final l10n = AppLocalizations.of(context)!;

    // 如果已经有server list URL，直接返回
    if (_serverListUrl.isNotEmpty) {
      _logService.info('LoginScreen', 'Server list URL already available: $_serverListUrl');
      return;
    }

    // 检查是否有保存的域名
    final currentDomain = _domainController.text.trim();
    if (currentDomain.isEmpty) {
      _logService.error('LoginScreen', 'No domain available for server list URL lookup');
      throw Exception(l10n.pleaseEnterClientDomain);
    }

    _logService.info('LoginScreen', 'Server list URL not available, attempting to fetch for domain: $currentDomain');

    try {
      // 重新查询服务器列表URL
      final lookupService = LookupService();
      final lookupResult = await lookupService.getServerListUrl(currentDomain);

      // 更新server list URL
      _serverListUrl = lookupResult.serverListUrl;

      // 如果是模糊匹配，更新域名
      if (lookupResult.isFuzzyMatch) {
        setState(() {
          _domainController.text = lookupResult.completeDomain;
        });
        _logService.info('LoginScreen', 'Fuzzy match detected during server list URL fetch, updated domain from "$currentDomain" to "${lookupResult.completeDomain}"');

        // 保存更新后的域名
        await _storageService.setString(StorageKeys.domain, lookupResult.completeDomain);
      }

      _logService.info('LoginScreen', 'Successfully fetched server list URL: ${lookupResult.serverListUrl}');

    } catch (e) {
      _logService.error('LoginScreen', 'Failed to fetch server list URL during login', e);

      String errorMessage;
      if (e is ApiException && mounted) {
        // 使用ApiException的本地化错误消息
        errorMessage = e.getUserFriendlyMessage(context);
      } else {
        // 使用通用的本地化错误消息
        errorMessage = l10n.lookupServiceError;
      }

      throw Exception(errorMessage);
    }
  }

  /// 处理域名输入下一步
  Future<void> _handleDomainNext() async {
    final l10n = AppLocalizations.of(context)!;

    if (_domainController.text.trim().isEmpty) {
      setState(() {
        _errorMessage = l10n.pleaseEnterClientDomain;
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      // 查询服务器列表URL
      final lookupService = LookupService();
      final lookupResult = await lookupService.getServerListUrl(_domainController.text.trim());

      // 暂存URL，等到后端启动后再设置
      _serverListUrl = lookupResult.serverListUrl;

      // 如果是模糊匹配，替换用户输入的域名为完整域名
      if (lookupResult.isFuzzyMatch) {
        setState(() {
          _domainController.text = lookupResult.completeDomain;
        });
        _logService.info('LoginScreen', 'Fuzzy match detected, replaced domain from "${lookupResult.originalDomain}" to "${lookupResult.completeDomain}"');
      }

      // 保存域名（使用可能已更新的完整域名）
      await _storageService.setString(StorageKeys.domain, _domainController.text.trim());

      // 进入下一步
      setState(() {
        _loginStep = 1;
      });

      _logService.info('LoginScreen', 'Domain lookup completed successfully, server URL cached: ${lookupResult.serverListUrl}');

    } catch (e) {
      _logService.error('LoginScreen', 'Domain lookup failed', e);
      String errorMessage;

      if (e is ApiException && mounted) {
        // 使用ApiException的本地化错误消息
        errorMessage = e.getUserFriendlyMessage(context);
      } else {
        // 使用通用的本地化错误消息
        errorMessage = l10n.lookupServiceError;
      }

      setState(() {
        _errorMessage = errorMessage;
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// Windows 后端服务优雅降级启动
  ///
  /// DESCRIPTION:
  ///     实现 Windows 服务化部署的优雅降级策略：
  ///     1. 健康检查成功 → 发送 disconnect，使用系统服务
  ///     2. 健康检查失败 → 发送 shutdown 清理进程，然后以管理员权限启动
  ///
  /// RETURNS:
  ///     Future<bool> - 启动成功返回true，失败返回false
  Future<bool> _startWindowsBackendWithGracefulDegradation() async {
    final apiService = serviceLocator<ApiService>();

    // 1. 健康检查：尝试连接系统服务
    _logService.info('LoginScreen', 'Checking if Windows system service is available...');
    try {
      final isHealthy = await apiService.healthCheck();
      if (isHealthy) {
        _logService.info('LoginScreen', 'Windows system service is available and healthy');

        // 发送 disconnect 确保没有活跃连接
        _logService.info('LoginScreen', 'Sending disconnect to ensure clean state...');
        try {
          await apiService.disconnect();
          _logService.info('LoginScreen', 'Disconnect sent successfully');
        } catch (e) {
          _logService.warning('LoginScreen', 'Disconnect failed (may be expected): $e');
        }

        // 显示状态提示
        if (mounted) {
          // TODO: 可以在UI上显示"使用系统服务"的状态
        }
        return true;
      }
    } catch (e) {
      _logService.info('LoginScreen', 'Windows system service health check failed: $e');
    }

    // 2. 健康检查失败：发送 shutdown 清理可能存在的进程
    _logService.info('LoginScreen', 'Health check failed, attempting to clean up existing processes...');
    try {
      await apiService.shutdownBackend();
      _logService.info('LoginScreen', 'Shutdown command sent successfully');
      // 等待进程清理完成
      await Future.delayed(const Duration(seconds: 2));
    } catch (e) {
      _logService.warning('LoginScreen', 'Shutdown command failed (may be expected): $e');
    }

    // 3. 权限启动：回退到原有的管理员权限启动方式
    _logService.info('LoginScreen', 'Falling back to admin privilege startup...');
    final backendService = serviceLocator<BackendService>();
    final backendStarted = await backendService.start();

    if (backendStarted) {
      _logService.info('LoginScreen', 'Backend service started successfully with admin privileges');
      // 显示状态提示
      if (mounted) {
        // TODO: 可以在UI上显示"使用管理员权限启动"的状态
      }
      return true;
    } else {
      _logService.error('LoginScreen', 'Failed to start backend service with admin privileges');
      return false;
    }
  }

  /// 保存凭据
  Future<void> _saveCredentials() async {
    try {
      final encryptor = SimpleEncryptor();

      // 保存域名
      await _storageService.setString(StorageKeys.domain, _domainController.text.trim());

      // 保存用户名
      await _storageService.setString(StorageKeys.username, _usernameController.text.trim());

      // 保存加密的密码到安全存储
      final encryptedPassword = await encryptor.encrypt(_passwordController.text);
      await _storageService.setSecureString(StorageKeys.password, encryptedPassword);

      _logService.info('LoginScreen', 'Credentials saved successfully');
    } catch (e) {
      _logService.error('LoginScreen', 'Failed to save credentials', e);
    }
  }

  /// 处理登录
  Future<void> _handleLogin() async {
    final l10n = AppLocalizations.of(context)!;

    if (_usernameController.text.trim().isEmpty) {
      setState(() => _errorMessage = l10n.pleaseEnterUsername);
      return;
    }

    if (_passwordController.text.trim().isEmpty) {
      setState(() => _errorMessage = l10n.pleaseEnterPassword);
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      _logService.info('LoginScreen', 'Starting login process - username: ${_usernameController.text.trim()}, domain: ${_domainController.text.trim()}, remember: $_rememberCredentials');

      // 获取服务实例
      final apiService = serviceLocator<ApiService>();
      final appState = serviceLocator<AppState>();
      final dataManager = serviceLocator<DataManager>();

      // 检查API服务状态
      _logService.info('LoginScreen', 'Checking API service health status');
      try {
        final isHealthy = await apiService.healthCheck();
        _logService.info('LoginScreen', 'API service health check result: $isHealthy');
      } catch (e) {
        _logService.warning('LoginScreen', 'API service health check failed: $e');
      }

      // 检查并确保server list URL可用
      await _ensureServerListUrl();

      // 1. 检查网络权限（仅iOS需要）
      if (Platform.isIOS) {
        _logService.info('LoginScreen', 'Checking network permission before backend startup...');
        final networkService = NetworkPermissionService.instance;
        final hasNetworkPermission = await networkService.checkNetworkPermission();

        if (!hasNetworkPermission) {
          _logService.warning('LoginScreen', 'Network permission not granted');
          setState(() => _errorMessage = '网络权限未授权，请在系统设置中允许应用访问网络');
          return;
        }
        _logService.info('LoginScreen', 'Network permission verified');
      }

      // 2. 启动后端服务（Windows和Apple平台都需要在登录时启动）
      if (Platform.isWindows) {
        _logService.info('LoginScreen', 'Starting Windows backend service with graceful degradation...');
        final backendStarted = await _startWindowsBackendWithGracefulDegradation();

        if (!backendStarted) {
          _logService.error('LoginScreen', 'Failed to start backend service');
          setState(() => _errorMessage = l10n.backendServiceStartFailed);
          return;
        }

        // Windows平台需要在后端启动后初始化API服务
        _logService.info('LoginScreen', 'Initializing API service after backend startup...');
        final apiInitialized = await apiService.initialize();
        if (!apiInitialized) {
          _logService.warning('LoginScreen', 'API service initialization returned false');
        }
      } else if (Platform.isIOS || Platform.isMacOS) {
        // ✅ Apple平台：后端服务只在用户登录时启动（符合设计原则）
        // 这里初始化PlatformChannelHandler，它会调用PanabitCoreManager.shared.initialize()
        // 但不会启动VPN服务，VPN服务将在PlatformChannelHandler.handleLogin()中启动
        final backendService = PlatformServiceFactory.createBackendService();
        _logService.info('LoginScreen', 'Starting Swift backend service on ${Platform.operatingSystem}...');

        // 获取平台信息用于调试
        // final platformInfo = backendService.getPlatformInfo();
        // _logService.debug('LoginScreen', 'Backend platform info: $platformInfo');

        final success = await backendService.initialize();
        if (success) {
          _logService.info('LoginScreen', 'Swift backend service initialized successfully');

          // 验证后端服务健康状态
          final isHealthy = await backendService.checkHealth();
          if (isHealthy) {
            _logService.info('LoginScreen', 'Backend service health check passed');
          } else {
            _logService.warning('LoginScreen', 'Backend service health check failed, but initialization succeeded');
          }
        } else {
          _logService.error('LoginScreen', 'Swift backend service failed to initialize');
          setState(() => _errorMessage = l10n.backendServiceStartFailed);
          return;
        }
      } else if (Platform.isAndroid) {
        // ✅ Android平台：后端服务只在用户登录时启动（符合设计原则）
        // 这里初始化PlatformChannelHandler，它会调用VPN服务初始化
        final backendService = PlatformServiceFactory.createBackendService();
        _logService.info('LoginScreen', 'Starting Android backend service...');

        final success = await backendService.initialize();
        if (success) {
          _logService.info('LoginScreen', 'Android backend service initialized successfully');

          // 验证后端服务健康状态
          final isHealthy = await backendService.checkHealth();
          if (isHealthy) {
            _logService.info('LoginScreen', 'Backend service health check passed');
          } else {
            _logService.warning('LoginScreen', 'Backend service health check failed, but initialization succeeded');
          }
        } else {
          _logService.error('LoginScreen', 'Android backend service failed to initialize');
          setState(() => _errorMessage = l10n.backendServiceStartFailed);
          return;
        }
      } else {
        _logService.info('LoginScreen', 'Backend service already running on ${Platform.operatingSystem}');
      }

      // 2. 检查后端健康状态
      _logService.info('LoginScreen', 'Checking backend service health...');
      bool isHealthy = false;
      for (int i = 0; i < 40; i++) {
        try {
          isHealthy = await apiService.healthCheck();
          if (isHealthy) break;
        } catch (e) {
          // 健康检查失败，继续重试
        }
        await Future.delayed(const Duration(milliseconds: 100));
      }

      if (!isHealthy) {
        _logService.error('LoginScreen', 'Backend service health check failed');
        setState(() => _errorMessage = l10n.backendServiceHealthCheckFailed);
        return;
      }

      // 3. 初始化WebSocket连接
      try {
        final websocketService = serviceLocator<WebSocketService>();
        final connected = await websocketService.connect();
        if (!connected) {
          _logService.warning('LoginScreen', 'WebSocket connection failed, will retry after login');
        }
      } catch (e) {
        _logService.warning('LoginScreen', 'WebSocket connection exception: $e');
      }

      // 3.5. 初始化路由设置（在设置客户域之前，确保等待完成）
      try {
        _logService.info('LoginScreen', 'Initializing routing settings...');
        final routingSettingsService = serviceLocator<RoutingSettingsService>();

        // 先尝试从本地加载路由设置
        _logService.info('LoginScreen', 'Loading routing settings from local storage...');
        final localSettings = await routingSettingsService.loadRoutingSettings();

        if (localSettings != null) {
          // 如果有本地设置，同步到后端并等待完成
          _logService.info('LoginScreen',
            'Found local routing settings - mode: ${localSettings.mode}, custom routes: ${localSettings.customRoutes}');
          _logService.info('LoginScreen', 'Syncing routing settings to backend, please wait...');

          await routingSettingsService.syncRoutingSettingsToBackend(localSettings);

          _logService.info('LoginScreen', 'Routing settings sync to backend completed');
        } else {
          // 如果没有本地设置，使用默认设置（不从后端获取）
          _logService.info('LoginScreen', 'No local routing settings found, will use default settings (all routing mode)');
        }

        _logService.info('LoginScreen', 'Routing settings initialization completed, continuing login process');
      } catch (e) {
        _logService.error('LoginScreen', 'Failed to initialize routing settings', e);
        // 显示警告但不阻止登录流程
        _logService.warning('LoginScreen', 'Routing settings initialization failed, will continue login with default settings');
      }

      // 4. 设置服务器列表URL（使用确保可用的URL）
      if (_serverListUrl.isNotEmpty) {
        try {
          await apiService.setServerProviderUrl(_serverListUrl);
          _logService.info('LoginScreen', 'Server provider URL set successfully: $_serverListUrl');
        } catch (e) {
          _logService.error('LoginScreen', 'Failed to set server provider URL', e);
          String errorMessage;

          if (e is ApiException && mounted) {
            // 使用ApiException的本地化错误消息
            errorMessage = e.getUserFriendlyMessage(context);
          } else {
            // 使用通用的错误消息
            errorMessage = 'Failed to set server provider URL: ${e.toString()}';
          }

          setState(() => _errorMessage = errorMessage);
          return;
        }
      } else {
        // 这种情况不应该发生，因为_ensureServerListUrl()应该已经确保URL可用
        _logService.error('LoginScreen', 'Server list URL is still empty after _ensureServerListUrl()');
        setState(() => _errorMessage = l10n.lookupServiceError);
        return;
      }

      // 4. 进行用户认证
      _logService.info('LoginScreen', 'Starting user authentication...');
      _logService.info('LoginScreen', 'Authentication parameters - username: ${_usernameController.text.trim()}, domain: ${_domainController.text.trim()}');

      try {
        await _authService.login(
          _usernameController.text.trim(),
          _passwordController.text,
          _rememberCredentials,
          domain: _domainController.text.trim(),
        );
        _logService.info('LoginScreen', 'User authentication completed successfully');
      } catch (e) {
        _logService.error('LoginScreen', 'User authentication failed', e);
        rethrow;
      }

      // 5. 保存凭据（如果用户选择记住）
      if (_rememberCredentials) {
        await _saveCredentials();
      }

      // 6. 通知WebSocket服务用户已登录
      try {
        final websocketService = serviceLocator<WebSocketService>();
        websocketService.notifyUserLoggedIn();
      } catch (e) {
        _logService.error('LoginScreen', 'Failed to notify WebSocket service of user login', e);
      }

      // 7. 初始化AppState
      appState.updateAuthenticationStatus(true);

      // 8. 设置用户信息
      await dataManager.loadUserInfo(_authService.username);
      if (appState.userInfo.displayName.isEmpty) {
        final userInfo = UserInfo(displayName: _authService.username);
        appState.updateUserInfo(userInfo);
        await dataManager.saveUserInfo(userInfo, _authService.username);
      }

      // 9. 导航到主屏幕
      if (mounted) {
        Navigator.of(context).pushReplacementNamed('/main');
      }
    } catch (e) {
      _logService.error('LoginScreen', 'Error occurred during login process', e);
      String errorMessage = l10n.loginFailed;
      if (e is ApiException && mounted) {
        errorMessage = e.getUserFriendlyMessage(context);
      } else {
        errorMessage = '${l10n.loginFailed}：${e.toString()}';
      }
      if (mounted) {
        setState(() => _errorMessage = errorMessage);
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}
